# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Lockbox is a Rails application for managing SSH keys and user access on servers. It provides a web interface for creating, distributing, and revoking SSH keys across multiple servers with features like two-factor authentication, key expiration, and audit logging.

## Commands

### Development
```bash
# Install dependencies
bundle install
yarn install

# Run development server (includes JS/CSS watching via foreman)
bin/dev

# Run Rails server only
bin/rails server

# Database setup
bin/rails db:create db:migrate db:seed

# Run Rails console
bin/rails console
```

### Testing
```bash
# Run all RSpec tests
bundle exec rspec

# Run specific test file
bundle exec rspec spec/controllers/keys_controller_spec.rb

# Run specific test line
bundle exec rspec spec/controllers/keys_controller_spec.rb:42

# Run system tests
bin/rails test:system
```

### Asset Building
```bash
# Build JavaScript
yarn build

# Build CSS
yarn build:css

# Watch mode for development
yarn build --watch
yarn watch:css
```

### Background Jobs
```bash
# Run GoodJob worker
bundle exec good_job

# Access GoodJob dashboard at /good_job in development
```

## Architecture

### Core Models
- **User**: Devise-based authentication with 2FA support. Has many keys and servers.
- **Key**: SSH public keys belonging to users. Can be deployed to multiple servers via MultiKey.
- **Server**: Target servers where keys are deployed. Uses Ansible for key distribution.
- **MultiKey**: Join table linking keys to servers with deployment status.

### Key Services
- **DistributeKeyJob**: Deploys SSH keys to servers using Ansible playbooks
- **DeleteKeyJob**: Removes SSH keys from servers
- **ExpireKeysJob**: Handles automatic key expiration
- **AddPassphraseJob**: Adds passphrase protection to keys

### Authentication Flow
1. Standard Devise login with email/password
2. If 2FA enabled, redirects to OTP verification
3. Custom SessionsController handles 2FA flow
4. Uses `otp_secret_key` and `second_factor_attempts_count` on User model

### Asset Pipeline
- JavaScript: esbuild via jsbundling-rails (app/javascript/application.js)
- CSS: Sass via cssbundling-rails (app/assets/stylesheets/application.sass.scss)
- Bootstrap 5.3.2 and FontAwesome 6.4.2 included

### Background Processing
- Uses GoodJob for async jobs
- Job errors tracked in JobError model
- Key distribution uses Ansible playbooks in lib/deploy/

### Security Features
- Rack::Attack configured for rate limiting
- Download tokens expire after set time
- IP tracking for key downloads (DownloadIp model)
- SSH key encryption support
- Paper Trail for audit logging

## Testing Approach
- RSpec for unit/integration tests
- Factory Bot factories in spec/factories/
- Database Cleaner for test isolation
- SimpleCov for coverage reporting
- Test files mirror app structure in spec/

## Deployment
- Production uses Docker (Dockerfile-production)
- GitLab CI pipeline deploys to Kubernetes
- PostgreSQL in production, SQLite3 in development/test
- Environment variables configured via .env files