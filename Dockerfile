FROM nexus.imtins.com:5556/ruby:3.4.5

RUN apt-get update -yqq
RUN apt-get install -yqq --no-install-recommends nodejs curl build-essential libpq-dev wget git iputils-ping
# Install yarn
RUN curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg -o /root/yarn-pubkey.gpg && apt-key add /root/yarn-pubkey.gpg
RUN echo "deb https://dl.yarnpkg.com/debian/ stable main" > /etc/apt/sources.list.d/yarn.list
RUN apt-get update && apt-get install -y --no-install-recommends yarn

WORKDIR /usr/src/app
COPY Gemfile* /usr/src/app/
RUN gem install bundler

RUN bundle install
RUN yarn install

RUN echo "StrictHostKeyChecking no" >> /etc/ssh/ssh_config
RUN echo "UserKnownHostsFile /dev/null" >> /etc/ssh/ssh_config

COPY . /usr/src/app/


CMD ["rails", "s", "-b", "0.0.0.0"]
