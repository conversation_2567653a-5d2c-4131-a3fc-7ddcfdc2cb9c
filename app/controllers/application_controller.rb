class ApplicationController < ActionController::Base
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery unless: -> { request.format.json? }
  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_paper_trail_whodunnit

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up) do |u|
      u.permit(:username, :email, :password, :password_confirmation)
    end
    devise_parameter_sanitizer.permit(:sign_in) do |u|
      u.permit(:email, :username, :email, :password, :remember_me)
    end
    devise_parameter_sanitizer.permit(:account_update) do |u|
      u.permit(:username, :email, :password, :password_confirmation, :current_password)
    end
  end

  def after_sign_out_path_for(resource_or_scope)
    flash[:notice] = "Signed out successfully!"
    redirect_to new_user_session_path
  end

  def after_sign_in_path_for(resource_or_scope)
    admin_path
  end

  def authenticate_admin
    unless current_user.try(:admin?)
      redirect_to admin_path
    end
  end

  def generate_key_name(key)
    # self.name = SecureRandom.urlsafe_base64(3)
    file = File.read('app/assets/animals.json')
    list = JSON.parse(file)
    # list.count = 236
    animal = rand 237
    name = "#{list[animal]}-#{key.user.username}"
    if !Key.exists?(name: name)
      return name
    else
      generate_key_name(key)
    end
  end

end
