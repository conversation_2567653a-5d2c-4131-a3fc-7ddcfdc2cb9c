source 'https://rubygems.org'
ruby '3.4.5'

gem 'bootstrap_form'
gem 'bundler'
gem 'devise'
gem 'dotenv-rails'
gem "font-awesome-rails"
gem 'friendly_id'
gem 'inifile'
gem 'jbuilder'
gem 'openssl'
gem 'paper_trail'
gem 'pg'
gem 'rack-attack'
gem 'rails'
gem 'rubysl-open3'
gem 'sass-rails'
gem 'simplecov', :require => false, :group => :test
gem 'whenever'
gem "sentry-ruby"
gem "sentry-rails"
gem 'rufus-scheduler'
gem 'toastr-rails'
gem 'net-ping'
gem 'mail'
gem 'cssbundling-rails'
gem 'jsbundling-rails'
gem 'devise-two-factor'
gem 'rqrcode'

group :production do
  gem 'rails_12factor'
end

group :development do
  gem 'letter_opener'
  gem 'pry'
  gem 'puma'
  gem 'web-console'
  gem 'listen'
  gem 'rack-mini-profiler'
  gem "pry-remote"
end

group :development, :test do
  gem 'byebug'
  gem 'minitest'
  gem 'rails-controller-testing'
  gem 'rspec-rails'
  gem 'sqlite3'
end

group :test do
  gem 'capybara'
  gem 'database_cleaner'
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'shoulda-matchers'
end

gem "good_job"
