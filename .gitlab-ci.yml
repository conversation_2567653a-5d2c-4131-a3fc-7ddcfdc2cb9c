include:
  - project: 'rails-apps/common'
    file: '/rails-rancher-gitlab-ci.yml'

variables:
  KUBE_CONTEXT: rails-apps/lockbox:rails-apps-digitalocean-prod

deploy_for_production:
  before_script: []
  stage: deploy
  image:
    name: bitnami/kubectl:latest
    entrypoint: [""]
  script:
    - kubectl config use-context $KUBE_CONTEXT
    - kubectl rollout restart deployment $CI_PROJECT_NAME --namespace=lockbox-production
    - kubectl rollout status deployment $CI_PROJECT_NAME --timeout=90s --namespace=lockbox-production
  environment:
    name: production
    kubernetes:
      namespace: lockbox-production
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  tags:
    - docker-tests
