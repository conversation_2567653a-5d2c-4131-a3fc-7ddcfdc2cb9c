Rails.application.routes.draw do
  get 'downloadpicker/:id' => 'keys#downloadpicker', as: 'downloadpicker'
  get 'downloads/:id' => 'keys#download', as: 'downloads'
  post 'downloads/:id' => 'keys#download', as: 'passphrase_setup'
  post 'keys/renew/:id' => 'keys#renew', as: 'renew'
  post 'keys/single/:id' => 'keys#single', as: 'single'
  get 'keys/job_error/:id' => 'keys#job_error', as: 'job_error'
  get 'keys/job_errors' => 'keys#job_errors', as: 'job_errors'
  post 'keys/delete_errors' => 'keys#delete_errors', as: 'delete_errors'
  post 'servers/sync' => 'servers#sync', as: 'sync'
  post 'keys/addsingle' => 'keys#add_single_server'#, as: 'addsingle'
  post 'keys/master' => 'keys#master'

  resources :keys
  resources :servers
  devise_for :users, controllers: {
      registrations: "registrations",
      passwords: "passwords",
      sessions: 'sessions'
    }

  devise_scope :user do
    get 'users/sign_out' => "devise/sessions#destroy"
  end

  root 'admin#index'
  get 'admin' => 'admin#index'
  get 'admin/edit' => 'admin#edit'
  patch 'admin/edit' => 'admin#update'
  delete 'admin/edit' => 'admin#destroy'
  get 'admin/confirm' => 'admin#confirm'
  get 'admin/unlock' => 'admin#unlock'
  get 'admin/new' => 'admin#new'
  post 'admin/new' => 'admin#create'
  resource :two_factor_settings, except: [:index, :show]

  authenticate :user, ->(user) { user.admin? } do
    mount GoodJob::Engine => 'good_job'
  end

end
